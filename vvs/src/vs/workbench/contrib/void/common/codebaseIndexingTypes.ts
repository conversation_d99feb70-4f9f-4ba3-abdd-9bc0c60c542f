/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Void Software. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { Event } from '../../../../base/common/event.js';
import { URI } from '../../../../base/common/uri.js';

export interface ICodeChunk {
	id: string;
	filePath: string;
	startLine: number;
	endLine: number;
	content: string;
	type: 'function' | 'class' | 'method' | 'interface' | 'type' | 'variable' | 'import' | 'export';
	name?: string;
	language: string;
	metadata?: {
		signature?: string;
		docstring?: string;
		complexity?: number;
		dependencies?: string[];
		exports?: string[];
	};
}

export interface IEmbeddingResult {
	chunkId: string;
	embedding: number[];
	model: string;
	tokensUsed?: number;
}

export interface IIndexingProgress {
	type: 'progress' | 'complete' | 'error' | 'idle';
	phase?: 'idle' | 'progress' | 'complete'; // Current indexing phase
	statusText?: string; // General status message

	// Chunk-level progress (from server)
	completedChunks?: number;
	totalChunks?: number;
	currentBatchNumber?: number;
	totalBatches?: number;
	successfullyStoredInBatch?: number;
	errorsInBatch?: number;

	// File-level progress (derived by client and updated by server)
	currentFileRelativePath?: string;
	fileStatus?: 'embedding_started' | 'embedding_progress' | 'file_completed' | 'file_error' | 'parsed' | 'parsing_error';
	fileErrorDetails?: string;

	// Overall file counts (managed by client)
	totalFilesToProcess?: number;
	filesParsedCount?: number;
	filesEmbeddedSuccessfullyCount?: number;
	filesWithEmbeddingsErrorsCount?: number;

	percentage: number; // Overall percentage, can be based on files or chunks
	processedFilePaths?: string[]; // Deprecated, use recentFiles
	recentlyProcessedFiles?: IRecentFileActivity[]; // For detailed recent activity display
	error?: string; // For overall process error
}

export interface IRecentFileActivity {
	filePath: string;
	fileName: string; // Extracted for easier display
	status: 'parsing' | 'parsed' | 'embedding' | 'indexed' | 'error' | 'queued';
	timestamp: Date;
	errorDetails?: string;
}

export interface ISearchResult {
	chunk: ICodeChunk;
	score: number;
	highlights?: string[];
}

export interface ICodebaseIndexingService {
	readonly _serviceBrand: undefined;

	// Events
	readonly onDidChangeProgress: Event<IIndexingProgress>;
	readonly onDidChangeIndexingStatus: Event<boolean>;

	// Indexing control
	startIndexing(paths?: URI[], options?: IIndexingOptions): Promise<void>;
	stopIndexing(): Promise<void>;
	clearIndex(): Promise<void>;

	// Status
	isIndexing(): boolean;
	getProgress(): IIndexingProgress;
	getIndexStats(): Promise<IIndexStats>;
	getRealVectorCountSync(): number;
	getExpectedVectorCount(): number;

	// Search
	search(query: string, options?: ISearchOptions): Promise<ISearchResult[]>;

	// File operations
	indexFile(uri: URI): Promise<void>;
	removeFile(uri: URI): Promise<void>;
	isFileIndexed(uri: URI): Promise<boolean>;
}

export interface IIndexingOptions {
	includePatterns?: string[];
	excludePatterns?: string[];
	languages?: string[];
	forceReindex?: boolean;
	maxFileSizeBytes?: number;
}

export interface ISearchOptions {
	limit?: number;
	filters?: {
		fileTypes?: string[];
		paths?: string[];
		languages?: string[];
	};
	includeContext?: boolean;
}

export interface IIndexStats {
	totalFiles: number;
	totalChunks: number;
	totalSize: number;
	lastUpdated: Date;
	languages: Map<string, number>;
}

export interface ITreeSitterService {
	readonly _serviceBrand: undefined;

	// Parse file
	parseFile(uri: URI, content: string): Promise<ICodeChunk[]>;

	// Language support
	getSupportedLanguages(): string[];
	isLanguageSupported(language: string): boolean;
}

export interface IEmbeddingCache {
	get(chunkId: string): Promise<number[] | null>;
	set(chunkId: string, embedding: number[]): Promise<void>;
	has(chunkId: string): Promise<boolean>;
	delete(chunkId: string): Promise<void>;
	clear(): Promise<void>;
}

export interface IVectorSearchEngine {
	addVector(id: string, vector: number[], metadata?: any): void;
	removeVector(id: string): void;
	search(queryVector: number[], k: number): Array<{ id: string; score: number; metadata?: any }>;
	clear(): void;
	size(): number;
}