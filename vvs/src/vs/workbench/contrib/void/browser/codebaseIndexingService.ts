/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Void Software. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ICodebaseIndexingService as ICodebaseIndexingServiceInterface, IIndexingProgress, IIndexingOptions, ISearchOptions, ISearchResult, IIndexStats, ICodeChunk, IEmbeddingCache, IVectorSearchEngine, ITreeSitterService as ITreeSitterServiceInterface, IRecentFileActivity } from '../common/codebaseIndexingTypes.js';
import { ITreeSitterService } from './treeSitterService.js';
import { IVoidWebSocketService } from './voidWebSocketService.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { IProgressService, ProgressLocation } from '../../../../platform/progress/common/progress.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { URI } from '../../../../base/common/uri.js';
import { CancellationTokenSource } from '../../../../base/common/cancellation.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { MessageType } from './voidWebSocketTypes.js';
import { IDisposable } from '../../../../base/common/lifecycle.js';
import { FileChangeType } from '../../../../platform/files/common/files.js';
import { RunOnceScheduler } from '../../../../base/common/async.js';

export const ICodebaseIndexingService = createDecorator<ICodebaseIndexingServiceInterface>('codebaseIndexingService');

// Constants
const STORAGE_KEY_INDEXED_FILES = 'codebaseIndexing.indexedFiles';
const STORAGE_KEY_INDEX_STATS = 'codebaseIndexing.stats';
const STORAGE_KEY_EMBEDDING_CACHE = 'codebaseIndexing.embeddingCache';

const DEFAULT_EXCLUDE_PATTERNS = [
	'node_modules',
	'.git',
	'dist',
	'build',
	'out',
	'.next',
	'.vscode',
	'coverage',
	'.nyc_output',
	'*.min.js',
	'*.map'
];

const DEFAULT_MAX_FILE_SIZE = 1024 * 1024; // 1MB
const DEFAULT_BATCH_SIZE = 10;

const SUPPORTED_EXTENSIONS = [
	'ts', 'tsx', 'js', 'jsx', 'py', 'java', 'c', 'cpp', 'cs', 'go', 'rs', 'php',
	'rb', 'swift', 'kt', 'scala', 'r', 'dart', 'lua', 'perl', 'rust', 'sh',
	'sql', 'html', 'css', 'scss', 'less', 'json', 'xml', 'yaml', 'yml', 'md',
	'vue', 'svelte', 'astro'
];

class EmbeddingCache implements IEmbeddingCache {
	private cache = new Map<string, number[]>();

	constructor(
		private readonly storageService: IStorageService
	) {
		// Load cache from storage
		const stored = this.storageService.get(STORAGE_KEY_EMBEDDING_CACHE, StorageScope.WORKSPACE);
		if (stored) {
			try {
				const parsed = JSON.parse(stored);
				Object.entries(parsed).forEach(([key, value]) => {
					this.cache.set(key, value as number[]);
				});
			} catch (e) {
				// Ignore parse errors
			}
		}
	}

	async get(chunkId: string): Promise<number[] | null> {
		return this.cache.get(chunkId) || null;
	}

	async set(chunkId: string, embedding: number[]): Promise<void> {
		this.cache.set(chunkId, embedding);
		this.persist();
	}

	async has(chunkId: string): Promise<boolean> {
		return this.cache.has(chunkId);
	}

	async delete(chunkId: string): Promise<void> {
		this.cache.delete(chunkId);
		this.persist();
	}

	async clear(): Promise<void> {
		this.cache.clear();
		this.persist();
	}

	private persist(): void {
		const obj: Record<string, number[]> = {};
		this.cache.forEach((value, key) => {
			obj[key] = value;
		});
		this.storageService.store(STORAGE_KEY_EMBEDDING_CACHE, JSON.stringify(obj), StorageScope.WORKSPACE, StorageTarget.MACHINE);
	}
}

class VectorSearchEngine implements IVectorSearchEngine {
	private vectors = new Map<string, { vector: number[]; metadata?: any }>();

	addVector(id: string, vector: number[], metadata?: any): void {
		this.vectors.set(id, { vector, metadata });
	}

	removeVector(id: string): void {
		this.vectors.delete(id);
	}

	search(queryVector: number[], k: number): Array<{ id: string; score: number; metadata?: any }> {
		const results: Array<{ id: string; score: number; metadata?: any }> = [];

		this.vectors.forEach((data, id) => {
			const score = this.cosineSimilarity(queryVector, data.vector);
			results.push({ id, score, metadata: data.metadata });
		});

		// Sort by score descending and take top k
		results.sort((a, b) => b.score - a.score);
		return results.slice(0, k);
	}

	clear(): void {
		this.vectors.clear();
	}

	size(): number {
		return this.vectors.size;
	}

	private cosineSimilarity(a: number[], b: number[]): number {
		if (a.length !== b.length) {
			throw new Error('Vectors must have the same length');
		}

		let dotProduct = 0;
		let normA = 0;
		let normB = 0;

		for (let i = 0; i < a.length; i++) {
			dotProduct += a[i] * b[i];
			normA += a[i] * a[i];
			normB += b[i] * b[i];
		}

		normA = Math.sqrt(normA);
		normB = Math.sqrt(normB);

		if (normA === 0 || normB === 0) {
			return 0;
		}

		return dotProduct / (normA * normB);
	}
}

export class CodebaseIndexingService extends Disposable implements ICodebaseIndexingServiceInterface {
	readonly _serviceBrand: undefined;

	private readonly _onDidChangeProgress = this._register(new Emitter<IIndexingProgress>());
	readonly onDidChangeProgress: Event<IIndexingProgress> = this._onDidChangeProgress.event;

	private readonly _onDidChangeIndexingStatus = this._register(new Emitter<boolean>());
	readonly onDidChangeIndexingStatus: Event<boolean> = this._onDidChangeIndexingStatus.event;

	private _isIndexing = false;

	// New state management for improved progress tracking
	private _totalFilesToProcess: number = 0;
	private _filesRequiringProcessingCount: number = 0; // Files that actually need parsing/embedding in current session
	private _filesParsedCount: number = 0;
	private _filesEmbeddedSuccessfullyCount: number = 0;
	private _filesWithEmbeddingsErrorsCount: number = 0;
	private _completedChunks: number = 0;
	private _totalChunks: number = 0;
	private _recentlyProcessedFilesMap = new Map<string, IRecentFileActivity>();
	private _currentOverallStatusText: string = 'Idle';

	// Track real vector counts from server
	private _realVectorCount = 0;
	private _expectedVectorCount = 0;

	private readonly indexedFiles = new Set<string>();
	private readonly parsedFiles = new Set<string>(); // Track files that have been parsed but not necessarily embedded
	private readonly embeddedFiles = new Set<string>(); // Track files that have been successfully embedded
	private readonly fileChunks = new Map<string, ICodeChunk[]>();
	private readonly embeddingCache: IEmbeddingCache;
	private readonly searchEngine: IVectorSearchEngine;
	private cancellationTokenSource?: CancellationTokenSource;

	// File watching for incremental indexing
	private fileWatchers = new Map<string, IDisposable>();
	private pendingFileUpdates = new Set<string>();
	private updateDebouncer: any;

	constructor(
		@ITreeSitterService private readonly treeSitterService: ITreeSitterServiceInterface,
		@IVoidWebSocketService private readonly webSocketService: IVoidWebSocketService,
		@ILogService private readonly logService: ILogService,
		@IFileService private readonly fileService: IFileService,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
		@IProgressService private readonly progressService: IProgressService,
		@INotificationService private readonly notificationService: INotificationService,
		@IStorageService private readonly storageService: IStorageService
	) {
		super();

		this.embeddingCache = new EmbeddingCache(storageService);
		this.searchEngine = new VectorSearchEngine();

		// Load indexed files from storage
		const storedFiles = this.storageService.get(STORAGE_KEY_INDEXED_FILES, StorageScope.WORKSPACE);
		if (storedFiles) {
			try {
				const files = JSON.parse(storedFiles);
				files.forEach((file: string) => this.indexedFiles.add(file));
			} catch (e) {
				// Ignore parse errors
			}
		}

		// Load embedded files from storage (files that have been successfully embedded)
		const storedEmbeddedFiles = this.storageService.get('codebaseIndexing.embeddedFiles', StorageScope.WORKSPACE);
		if (storedEmbeddedFiles) {
			try {
				const files = JSON.parse(storedEmbeddedFiles);
				files.forEach((file: string) => this.embeddedFiles.add(file));
			} catch (e) {
				// Ignore parse errors
			}
		}

		// Initialize file watching
		this.initializeFileWatching();

		this.logService.info('[CodebaseIndexingService] Initialized');
	}

	private initializeFileWatching(): void {
		// Watch workspace folders for changes
		const workspaceFolders = this.workspaceService.getWorkspace().folders;
		for (const folder of workspaceFolders) {
			this.watchFolder(folder.uri);
		}

		// Listen for workspace folder changes
		this._register(this.workspaceService.onDidChangeWorkspaceFolders(e => {
			// Stop watching removed folders
			for (const removed of e.removed) {
				const watcher = this.fileWatchers.get(removed.uri.toString());
				if (watcher) {
					watcher.dispose();
					this.fileWatchers.delete(removed.uri.toString());
				}
			}

			// Start watching added folders
			for (const added of e.added) {
				this.watchFolder(added.uri);
			}
		}));

		// Set up debouncer for file updates
		this.updateDebouncer = this._register(new RunOnceScheduler(() => {
			this.processPendingFileUpdates();
		}, 1000)); // 1 second debounce
	}

	private watchFolder(folderUri: URI): void {
		const watcher = this.fileService.watch(folderUri, {
			recursive: true,
			excludes: DEFAULT_EXCLUDE_PATTERNS
		});

		this.fileWatchers.set(folderUri.toString(), watcher);

		// Listen for file changes
		this._register(this.fileService.onDidFilesChange(e => {
			// Check each affected resource
			if (e.affects(folderUri, FileChangeType.ADDED, FileChangeType.UPDATED, FileChangeType.DELETED)) {
				// Process added files
				for (const added of e.rawAdded) {
					if (this.shouldIndexFile(added)) {
						this.pendingFileUpdates.add(added.toString());
						this.updateDebouncer.schedule();
					}
				}

				// Process updated files
				for (const updated of e.rawUpdated) {
					if (this.indexedFiles.has(updated.toString()) && this.shouldIndexFile(updated)) {
						this.pendingFileUpdates.add(updated.toString());
						this.updateDebouncer.schedule();
					}
				}

				// Process deleted files
				for (const deleted of e.rawDeleted) {
					if (this.indexedFiles.has(deleted.toString())) {
						this.removeFile(deleted);
					}
				}
			}
		}));
	}

	private async processPendingFileUpdates(): Promise<void> {
		if (this.pendingFileUpdates.size === 0 || this._isIndexing) {
			return;
		}

		const filesToUpdate = Array.from(this.pendingFileUpdates);
		this.pendingFileUpdates.clear();

		this.logService.info(`[CodebaseIndexingService] Processing ${filesToUpdate.length} file updates`);

		for (const fileUri of filesToUpdate) {
			try {
				await this.indexFile(URI.parse(fileUri));
			} catch (error) {
				this.logService.error(`[CodebaseIndexingService] Error updating file ${fileUri}:`, error);
			}
		}
	}

	private shouldIndexFile(uri: URI): boolean {
		const path = uri.fsPath;

		// Check file extension
		const ext = path.split('.').pop()?.toLowerCase();
		if (!ext || !SUPPORTED_EXTENSIONS.includes(ext)) {
			return false;
		}

		// Check exclude patterns
		for (const pattern of DEFAULT_EXCLUDE_PATTERNS) {
			if (path.includes(pattern)) {
				return false;
			}
		}

		return true;
	}

	async startIndexing(paths?: URI[], options?: IIndexingOptions): Promise<void> {
		if (this._isIndexing) {
			this.notificationService.warn('Indexing is already in progress');
			return;
		}

		this._isIndexing = true;
		this._onDidChangeIndexingStatus.fire(true);
		this.cancellationTokenSource = new CancellationTokenSource();

		// Reset state for new indexing session
		this._totalFilesToProcess = 0;
		this._filesRequiringProcessingCount = 0;
		this._filesParsedCount = 0;
		this._filesEmbeddedSuccessfullyCount = 0;
		this._filesWithEmbeddingsErrorsCount = 0;
		this._completedChunks = 0;
		this._totalChunks = 0;
		this._recentlyProcessedFilesMap.clear();
		this._currentOverallStatusText = 'Collecting files...';

		const workspaceFolders = this.workspaceService.getWorkspace().folders;
		if (workspaceFolders.length === 0) {
			this.notificationService.error('No workspace folder open');
			this.stopIndexing();
			return;
		}

		const targetPaths = paths || workspaceFolders.map(f => f.uri);
		const excludePatterns = options?.excludePatterns || DEFAULT_EXCLUDE_PATTERNS;
		const maxFileSize = options?.maxFileSizeBytes || DEFAULT_MAX_FILE_SIZE;

		this.logService.info(`[CodebaseIndexingService] Starting indexing for ${targetPaths.length} paths`);

		await this.progressService.withProgress({
			location: ProgressLocation.Notification,
			title: 'Indexing codebase',
			cancellable: true
		}, async (progress) => {
			const token = new CancellationTokenSource().token;
			token.onCancellationRequested(() => {
				this.cancellationTokenSource?.cancel();
			});

			try {
				// Phase 1: Collect files
				this._emitProgressUpdate();
				progress.report({ increment: 0, message: 'Collecting files...' });

				const files = await this.collectFiles(targetPaths, excludePatterns, maxFileSize);
				this._totalFilesToProcess = files.length;
				this.logService.info(`[CodebaseIndexingService] Found ${files.length} files to index`);

				if (files.length === 0) {
					this._currentOverallStatusText = 'No files found to index';
					this._emitProgressUpdate();
					this.notificationService.info('No files found to index');
					this.stopIndexing();
					return;
				}

				// Phase 2: Parse files (only files that haven't been embedded yet)
				const allChunks: Array<{ uri: URI; chunks: ICodeChunk[] }> = [];
				const filesToProcess = files.filter(file => !this.embeddedFiles.has(file.fsPath));
				this._filesRequiringProcessingCount = filesToProcess.length;

				this.logService.info(`[CodebaseIndexingService] ${filesToProcess.length} files need embedding (${files.length - filesToProcess.length} already embedded)`);

				for (const file of filesToProcess) {
					if (this.cancellationTokenSource?.token.isCancellationRequested) {
						break;
					}

					const fileName = file.path.split('/').pop() || file.fsPath;

					try {
						// Update status for this file
						this._currentOverallStatusText = `Parsing ${this._filesParsedCount + 1} of ${filesToProcess.length} files: ${fileName}`;
						this._recentlyProcessedFilesMap.set(file.fsPath, {
							filePath: file.fsPath,
							fileName,
							status: 'parsing',
							timestamp: new Date()
						});
						this._emitProgressUpdate();

						const chunks = await this.parseFile(file);
						if (chunks.length > 0) {
							allChunks.push({ uri: file, chunks });
							this.fileChunks.set(file.toString(), chunks);
							this.parsedFiles.add(file.fsPath);
						}

						this._filesParsedCount++;

						// Update file status to parsed
						this._recentlyProcessedFilesMap.set(file.fsPath, {
							filePath: file.fsPath,
							fileName,
							status: 'parsed',
							timestamp: new Date()
						});

					} catch (error) {
						this.logService.error(`[CodebaseIndexingService] Error parsing file ${file.fsPath}:`, error);

						// Update file status to error (parsing errors don't count as embedding errors)
						this._recentlyProcessedFilesMap.set(file.fsPath, {
							filePath: file.fsPath,
							fileName,
							status: 'error',
							timestamp: new Date(),
							errorDetails: error instanceof Error ? error.message : String(error)
						});
						// Note: Don't increment _filesWithEmbeddingsErrorsCount for parsing errors
					}

					const parseProgress = (this._filesParsedCount / filesToProcess.length) * 30; // First 30% for parsing
					progress.report({ increment: parseProgress / filesToProcess.length * 100, message: `Parsing files... (${this._filesParsedCount}/${filesToProcess.length})` });
				}

				this._totalChunks = allChunks.reduce((sum, item) => sum + item.chunks.length, 0);
				this._expectedVectorCount = this._totalChunks; // Track expected vectors
				this.logService.info(`[CodebaseIndexingService] Parsed ${this._filesParsedCount} files, found ${this._totalChunks} chunks`);

				// Phase 3: Generate embeddings
				this._currentOverallStatusText = 'Generating embeddings...';
				this._emitProgressUpdate();
				progress.report({ message: 'Generating embeddings...' });

				const batchSize = DEFAULT_BATCH_SIZE; // Ensure batchSize is defined here

				for (let i = 0; i < allChunks.length; i += batchSize) {
					if (this.cancellationTokenSource?.token.isCancellationRequested) {
						break;
					}

					const batch = allChunks.slice(i, i + batchSize);
					const chunksInBatch = batch.flatMap(item => item.chunks);

					try {
						await this.generateEmbeddings(chunksInBatch); // This promise now resolves after server confirms embeddings

						// Mark files in this batch as indexed in the indexedFiles set
						batch.forEach(item => {
							this.indexedFiles.add(item.uri.fsPath);
							this.embeddedFiles.add(item.uri.fsPath); // Track as successfully embedded
						});

					} catch (error) {
						this.logService.error(`[CodebaseIndexingService] Error generating embeddings for a batch:`, error);
						// Error handling is done in generateEmbeddings method via server progress messages
					}

					// Periodically update real vector count for accurate progress
					if (i % (batchSize * 2) === 0) { // Every 2 batches
						this.updateRealVectorCount();
					}

					// Progress is updated via server progress messages in generateEmbeddings method
					progress.report({ message: `Generating embeddings... (${this._completedChunks}/${this._totalChunks})` });
				}

				// Final stats update - all progress is now managed via _recentlyProcessedFilesMap
				const finalEmbeddedFiles = this.embeddedFiles.size;

				// Save indexed files
				this.storageService.store(
					STORAGE_KEY_INDEXED_FILES,
					JSON.stringify(Array.from(this.indexedFiles)),
					StorageScope.WORKSPACE,
					StorageTarget.MACHINE
				);

				// Save embedded files (successfully embedded)
				this.storageService.store(
					'codebaseIndexing.embeddedFiles',
					JSON.stringify(Array.from(this.embeddedFiles)),
					StorageScope.WORKSPACE,
					StorageTarget.MACHINE
				);

				// Save stats
				const stats: IIndexStats = {
					totalFiles: finalEmbeddedFiles, // Use embedded files count for accuracy
					totalChunks: this._completedChunks, // Use server-reported completed chunks
					totalSize: this.searchEngine.size(),
					lastUpdated: new Date(),
					languages: new Map()
				};
				this.storageService.store(
					STORAGE_KEY_INDEX_STATS,
					JSON.stringify({
						...stats,
						languages: Array.from(stats.languages.entries())
					}),
					StorageScope.WORKSPACE,
					StorageTarget.MACHINE
				);

				// Get final real vector count from server
				try {
					const finalRealVectorCount = await this.getRealVectorCount();
					this._realVectorCount = finalRealVectorCount;
					this.logService.info(`[CodebaseIndexingService] Final vector count from Pinecone: ${finalRealVectorCount}`);
				} catch (error) {
					this.logService.debug('[CodebaseIndexingService] Could not get final vector count:', error);
				}

				// Update final status
				this._currentOverallStatusText = 'Indexing complete';
				this._emitProgressUpdate();

				// Provide more detailed completion message
				const skippedFiles = this._totalFilesToProcess - filesToProcess.length;
				let message = `Indexed ${this._filesEmbeddedSuccessfullyCount} files`;
				if (this._filesWithEmbeddingsErrorsCount > 0) {
					message += ` (${this._filesWithEmbeddingsErrorsCount} files had errors)`;
				}
				if (skippedFiles > 0) {
					message += ` (${skippedFiles} files were already indexed)`;
				}
				if (this._realVectorCount > 0 && this._realVectorCount !== this._completedChunks) {
					message += ` (${this._realVectorCount} vectors stored in cloud)`;
				}
				this.notificationService.info(message);

			} catch (error) {
				this.logService.error('[CodebaseIndexingService] Indexing error:', error);
				this._currentOverallStatusText = 'Indexing failed';
				this._emitProgressUpdate();
				this.notificationService.error('Indexing failed: ' + (error instanceof Error ? error.message : String(error)));
			} finally {
				this.stopIndexing();
			}
		});
	}

	async stopIndexing(): Promise<void> {
		this.cancellationTokenSource?.cancel();
		this._isIndexing = false;
		this._onDidChangeIndexingStatus.fire(false);
		this._currentOverallStatusText = 'Idle';
		this._emitProgressUpdate();
	}

	async clearIndex(): Promise<void> {
		try {
			// First, clear local storage and caches
			this.indexedFiles.clear();
			this.parsedFiles.clear();
			this.embeddedFiles.clear();
			this.fileChunks.clear();
			await this.embeddingCache.clear();
			this.searchEngine.clear();

			this.storageService.remove(STORAGE_KEY_INDEXED_FILES, StorageScope.WORKSPACE);
			this.storageService.remove('codebaseIndexing.embeddedFiles', StorageScope.WORKSPACE);
			this.storageService.remove(STORAGE_KEY_INDEX_STATS, StorageScope.WORKSPACE);
			this.storageService.remove(STORAGE_KEY_EMBEDDING_CACHE, StorageScope.WORKSPACE);

			// Reset all state variables
			this._totalFilesToProcess = 0;
			this._filesRequiringProcessingCount = 0;
			this._filesParsedCount = 0;
			this._filesEmbeddedSuccessfullyCount = 0;
			this._filesWithEmbeddingsErrorsCount = 0;
			this._completedChunks = 0;
			this._totalChunks = 0;
			this._recentlyProcessedFilesMap.clear();
			this._currentOverallStatusText = 'Clearing index...';
			this._realVectorCount = 0;
			this._expectedVectorCount = 0;

			this._emitProgressUpdate();

			// Send clear index request to server to delete from Pinecone
			const requestId = generateUuid();
			await this.webSocketService.sendMessage({
				type: MessageType.CODEBASE_CLEAR_INDEX_REQUEST,
				payload: {
					requestId
				}
			});

			// Wait for server response
			return new Promise((resolve, reject) => {
				const disposable = this.webSocketService.onMessage((message: any) => {
					if (message.type === MessageType.CODEBASE_CLEAR_INDEX_RESPONSE &&
						message.payload?.requestId === requestId) {
						disposable.dispose();

						if (message.payload.success) {
							const deletedCount = message.payload.deletedVectorCount || 0;
							this._currentOverallStatusText = 'Idle';
							this._emitProgressUpdate();

							let successMessage = 'Index cleared successfully';
							if (deletedCount > 0) {
								successMessage += ` (${deletedCount} vectors deleted from cloud storage)`;
							}
							this.notificationService.info(successMessage);
							resolve();
						} else {
							const error = message.payload.error || 'Failed to clear index from server';
							this.logService.error('[CodebaseIndexingService] Server clear index failed:', error);
							this._currentOverallStatusText = 'Idle';
							this._emitProgressUpdate();
							this.notificationService.error(`Failed to clear cloud index: ${error}`);
							reject(new Error(error));
						}
					}
				});

				// Timeout after 30 seconds
				setTimeout(() => {
					disposable.dispose();
					this._currentOverallStatusText = 'Idle';
					this._emitProgressUpdate();
					this.notificationService.warn('Clear index request timed out, but local cache was cleared');
					resolve(); // Don't reject on timeout, local clear was successful
				}, 30000);
			});

		} catch (error) {
			this._currentOverallStatusText = 'Idle';
			this._emitProgressUpdate();
			this.logService.error('[CodebaseIndexingService] Error clearing index:', error);
			this.notificationService.error('Failed to clear index: ' + (error instanceof Error ? error.message : String(error)));
			throw error;
		}
	}

	isIndexing(): boolean {
		return this._isIndexing;
	}

	getProgress(): IIndexingProgress {
		// Determine current phase
		let phase: IIndexingProgress['phase'] = 'idle';
		let percentage = 0;

		if (this._isIndexing) {
			if (this._currentOverallStatusText.includes('Collecting')) {
				phase = 'progress';
				percentage = 5; // Small fixed value for collecting phase
			} else if (this._filesParsedCount < this._filesRequiringProcessingCount) {
				phase = 'progress';
				// Parsing phase: 30% of total progress
				percentage = this._filesRequiringProcessingCount > 0 ?
					Math.round((this._filesParsedCount / this._filesRequiringProcessingCount) * 30) : 0;
			} else if (this._completedChunks < this._totalChunks) {
				phase = 'progress';
				// Embedding phase: 30% (parsing) + 70% (embedding progress)
				const embeddingProgress = this._totalChunks > 0 ?
					(this._completedChunks / this._totalChunks) * 70 : 0;
				percentage = Math.round(30 + embeddingProgress);
			} else {
				phase = 'complete';
				percentage = 100;
			}
		}

		// Get recent files array from map, sorted by timestamp (most recent first)
		const recentlyProcessedFiles = Array.from(this._recentlyProcessedFilesMap.values())
			.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
			.slice(0, 15); // Limit to last 15 files

		return {
			type: 'progress',
			phase,
			percentage,
			statusText: this._currentOverallStatusText,
			totalFilesToProcess: this._totalFilesToProcess,
			filesParsedCount: this._filesParsedCount,
			filesEmbeddedSuccessfullyCount: this._filesEmbeddedSuccessfullyCount,
			filesWithEmbeddingsErrorsCount: this._filesWithEmbeddingsErrorsCount,
			completedChunks: this._completedChunks,
			totalChunks: this._totalChunks,
			recentlyProcessedFiles
		};
	}

	/**
	 * Get the current real vector count from Pinecone
	 */
	getRealVectorCountSync(): number {
		return this._realVectorCount;
	}

	/**
	 * Get the expected vector count (total chunks to be processed)
	 */
	getExpectedVectorCount(): number {
		return this._expectedVectorCount;
	}

	async getIndexStats(): Promise<IIndexStats> {
		// Always try to get real stats from server first
		try {
			const realStats = await this.getRealIndexStatsFromServer();
			if (realStats) {
				// Update stored stats with real data
				this.storageService.store(
					STORAGE_KEY_INDEX_STATS,
					JSON.stringify({
						...realStats,
						languages: Array.from(realStats.languages.entries())
					}),
					StorageScope.WORKSPACE,
					StorageTarget.MACHINE
				);
				return realStats;
			}
		} catch (error) {
			this.logService.debug('[CodebaseIndexingService] Could not get real stats from server:', error);
		}

		// Fallback to stored stats
		const stored = this.storageService.get(STORAGE_KEY_INDEX_STATS, StorageScope.WORKSPACE);
		if (stored) {
			try {
				const parsed = JSON.parse(stored);
				return {
					...parsed,
					lastUpdated: new Date(parsed.lastUpdated),
					languages: new Map(parsed.languages),
					totalSize: this.searchEngine.size() // Always use current search engine size
				};
			} catch (e) {
				// Ignore parse errors
			}
		}

		// Return current state even if no stored stats
		return {
			totalFiles: this.embeddedFiles.size, // Use embedded files for accuracy
			totalChunks: this.searchEngine.size(),
			totalSize: this.searchEngine.size(),
			lastUpdated: new Date(),
			languages: new Map()
		};
	}

	/**
	 * Get real indexing statistics from the server (Pinecone storage)
	 */
	private async getRealIndexStatsFromServer(): Promise<IIndexStats | null> {
		try {
			const requestId = generateUuid();

			// Send request for user namespace stats
			await this.webSocketService.sendMessage({
				type: MessageType.CODEBASE_SEARCH_REQUEST,
				payload: {
					query: '__GET_STATS__', // Special query to get stats
					requestId,
					options: { limit: 0 } // No actual search results needed
				}
			});

			// Wait for response
			return new Promise((resolve) => {
				const disposable = this.webSocketService.onMessage((message: any) => {
					if (message.type === MessageType.CODEBASE_SEARCH_RESPONSE &&
						message.payload?.requestId === requestId) {
						disposable.dispose();

						// If we get stats in the response metadata, use them
						if (message.payload.stats) {
							const stats = message.payload.stats;
							const realStats: IIndexStats = {
								totalFiles: this.embeddedFiles.size, // Use embedded files for accuracy
								totalChunks: stats.vectorCount || 0,
								totalSize: stats.vectorCount || 0,
								lastUpdated: new Date(),
								languages: new Map()
							};

							// Update real vector count for progress calculation
							this._realVectorCount = stats.vectorCount || 0;

							this.logService.info(`[CodebaseIndexingService] Real stats from Pinecone: ${stats.vectorCount} vectors in namespace ${stats.namespace}`);
							resolve(realStats);
						} else {
							resolve(null);
						}
					}
				});

				// Timeout after 10 seconds
				setTimeout(() => {
					disposable.dispose();
					resolve(null);
				}, 10000);
			});

		} catch (error) {
			this.logService.debug('[CodebaseIndexingService] Error getting real stats from server:', error);
			return null;
		}
	}

	/**
	 * Get real-time vector count from server for progress tracking
	 */
	private async getRealVectorCount(): Promise<number> {
		try {
			const stats = await this.getRealIndexStatsFromServer();
			return stats?.totalChunks || 0;
		} catch (error) {
			this.logService.debug('[CodebaseIndexingService] Error getting real vector count:', error);
			return this._realVectorCount; // Return cached value
		}
	}

	/**
	 * Update real vector count asynchronously (non-blocking)
	 */
	private updateRealVectorCount(): void {
		this.getRealVectorCount().then(count => {
			this._realVectorCount = count;
			this.logService.debug(`[CodebaseIndexingService] Updated real vector count: ${count}`);
		}).catch(error => {
			this.logService.debug('[CodebaseIndexingService] Failed to update real vector count:', error);
		});
	}

	/**
	 * Central helper to emit progress updates with current state
	 */
	private _emitProgressUpdate(): void {
		// Use the same logic as getProgress() for consistency
		const progress = this.getProgress();
		this._onDidChangeProgress.fire(progress);
	}

	async search(query: string, options?: ISearchOptions): Promise<ISearchResult[]> {
		try {
			// Use server-side search via WebSocket
			const requestId = generateUuid();

			// Send search request to server
			await this.webSocketService.sendMessage({
				type: MessageType.CODEBASE_SEARCH_REQUEST,
				payload: {
					query,
					requestId,
					options: {
						limit: options?.limit,
						filters: options?.filters
					}
				}
			});

			// Wait for response
			return new Promise((resolve, reject) => {
				const disposable = this.webSocketService.onMessage((message: any) => {
					if (message.type === MessageType.CODEBASE_SEARCH_RESPONSE &&
						message.payload?.requestId === requestId) {
						disposable.dispose();

						if (message.payload.error) {
							this.logService.error(`[CodebaseIndexingService] Search error: ${message.payload.error}`);
							resolve([]);
						} else {
							// Convert server response to ISearchResult format
							const results: ISearchResult[] = message.payload.results.map((result: any) => ({
								chunk: result.chunk,
								score: result.score,
								highlights: result.highlights || []
							}));

							this.logService.info(`[CodebaseIndexingService] Search completed with ${results.length} results`);
							resolve(results);
						}
					}
				});

				// Timeout after 30 seconds
				setTimeout(() => {
					disposable.dispose();
					this.logService.error('[CodebaseIndexingService] Search request timed out');
					resolve([]);
				}, 30000);
			});

		} catch (error) {
			this.logService.error('[CodebaseIndexingService] Search error:', error);
			return [];
		}
	}

	async indexFile(uri: URI): Promise<void> {
		try {
			const chunks = await this.parseFile(uri);
			if (chunks.length > 0) {
				this.parsedFiles.add(uri.fsPath);
				await this.generateEmbeddings(chunks);
				this.fileChunks.set(uri.toString(), chunks);
				this.indexedFiles.add(uri.toString());
				this.embeddedFiles.add(uri.fsPath);
			}
		} catch (error) {
			this.logService.error(`[CodebaseIndexingService] Error indexing file ${uri.fsPath}:`, error);
			throw error;
		}
	}

	async removeFile(uri: URI): Promise<void> {
		const uriStr = uri.toString();
		const filePath = uri.fsPath;
		const chunks = this.fileChunks.get(uriStr);
		if (chunks) {
			// Remove from search engine
			for (const chunk of chunks) {
				this.searchEngine.removeVector(chunk.id);
				await this.embeddingCache.delete(chunk.id);
			}
			this.fileChunks.delete(uriStr);
		}
		this.indexedFiles.delete(uriStr);
		this.parsedFiles.delete(filePath);
		this.embeddedFiles.delete(filePath);
	}

	async isFileIndexed(uri: URI): Promise<boolean> {
		return this.embeddedFiles.has(uri.fsPath);
	}



	private async collectFiles(paths: URI[], excludePatterns: string[], maxFileSize: number): Promise<URI[]> {
		const files: URI[] = [];
		const supportedExtensions = new Set([
			'.ts', '.tsx', '.js', '.jsx',
			'.py', '.java', '.cpp', '.cc', '.cxx', '.c', '.h', '.hpp',
			'.cs', '.go', '.rs', '.php', '.rb', '.swift', '.kt', '.scala'
		]);

		for (const path of paths) {
			try {
				const stat = await this.fileService.stat(path);
				if (stat.isDirectory) {
					// Recursively collect files
					await this.collectFilesRecursive(path, files, excludePatterns, supportedExtensions, maxFileSize);
				} else if (stat.isFile) {
					const ext = '.' + path.path.split('.').pop();
					if (supportedExtensions.has(ext) && stat.size <= maxFileSize) {
						files.push(path);
					}
				}
			} catch (error) {
				this.logService.error(`[CodebaseIndexingService] Error collecting files from ${path.fsPath}:`, error);
			}
		}

		return files;
	}

	private async collectFilesRecursive(
		dir: URI,
		files: URI[],
		excludePatterns: string[],
		supportedExtensions: Set<string>,
		maxFileSize: number
	): Promise<void> {
		try {
			const children = await this.fileService.resolve(dir);
			if (children.children) {
				for (const child of children.children) {
					const childUri = child.resource;

					// Check exclude patterns
					const shouldExclude = excludePatterns.some(pattern => {
						// Simple pattern matching (could be improved with glob)
						return childUri.path.includes(pattern.replace(/\*/g, ''));
					});

					if (shouldExclude) continue;

					if (child.isFile) {
						const ext = '.' + child.name.split('.').pop();
						if (supportedExtensions.has(ext)) {
							try {
								const stat = await this.fileService.stat(childUri);
								if (stat.size <= maxFileSize) {
									files.push(childUri);
								}
							} catch (e) {
								// Ignore stat errors
							}
						}
					} else if (child.isDirectory) {
						await this.collectFilesRecursive(childUri, files, excludePatterns, supportedExtensions, maxFileSize);
					}
				}
			}
		} catch (error) {
			this.logService.error(`[CodebaseIndexingService] Error reading directory ${dir.fsPath}:`, error);
		}
	}

	private async parseFile(uri: URI): Promise<ICodeChunk[]> {
		try {
			const content = await this.fileService.readFile(uri);
			const text = content.value.toString();
			return await this.treeSitterService.parseFile(uri, text);
		} catch (error) {
			this.logService.error(`[CodebaseIndexingService] Error parsing file ${uri.fsPath}:`, error);
			return [];
		}
	}

	private async generateEmbeddings(chunks: ICodeChunk[]): Promise<void> {
		if (chunks.length === 0) return;

		const requestId = generateUuid();
		const batchId = generateUuid();

		// Get unique file paths from chunks for tracking
		const filePaths = Array.from(new Set(chunks.map(chunk => chunk.filePath)));

		// Mark all files as embedding in the recent files map
		filePaths.forEach(filePath => {
			const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || filePath;
			this._recentlyProcessedFilesMap.set(filePath, {
				filePath,
				fileName,
				status: 'embedding',
				timestamp: new Date()
			});
		});

		// Send batch embedding request
		await this.webSocketService.sendMessage({
			type: MessageType.CODEBASE_EMBEDDING_BATCH_REQUEST,
			payload: {
				chunks,
				requestId,
				batchId
			}
		});

		// Wait for response with progress tracking
		return new Promise((resolve, reject) => {
			let progressDisposable: IDisposable | undefined;
			let responseDisposable: IDisposable | undefined;

			// Handle progress updates from server
			progressDisposable = this.webSocketService.onMessage((message: any) => {
				if (message.type === MessageType.CODEBASE_EMBEDDING_PROGRESS &&
					message.payload?.requestId === requestId) {

					const {
						completed,
						total,
						currentBatch,
						totalBatches,
						percentage,
						currentFileRelativePath,
						fileStatus,
						fileErrorDetails
					} = message.payload;

					this.logService.info(`[CodebaseIndexingService] Embedding progress: ${completed}/${total} chunks (${percentage}%) - Batch ${currentBatch}/${totalBatches}`);

					// Update chunk-level progress
					this._completedChunks = completed;
					this._totalChunks = Math.max(this._totalChunks, total);

					// Handle file-level progress updates
					if (currentFileRelativePath) {
						const fileName = currentFileRelativePath.split('/').pop() || currentFileRelativePath;

						// Update or create file entry in recent files map
						let newStatus: IRecentFileActivity['status'] = 'embedding';

						if (fileStatus === 'file_completed') {
							newStatus = 'indexed';
							this._filesEmbeddedSuccessfullyCount++;
						} else if (fileStatus === 'file_error') {
							newStatus = 'error';
							this._filesWithEmbeddingsErrorsCount++;
						}

						this._recentlyProcessedFilesMap.set(currentFileRelativePath, {
							filePath: currentFileRelativePath,
							fileName,
							status: newStatus,
							timestamp: new Date(),
							errorDetails: fileErrorDetails
						});

						// Update overall status text
						if (fileStatus === 'file_completed') {
							this._currentOverallStatusText = `Indexed ${this._filesEmbeddedSuccessfullyCount} of ${this._totalFilesToProcess} files`;
						} else if (fileStatus === 'file_error') {
							this._currentOverallStatusText = `Processing files... (${this._filesWithEmbeddingsErrorsCount} errors)`;
						} else {
							this._currentOverallStatusText = `Embedding file: ${fileName}`;
						}
					}

					this._emitProgressUpdate();
				}
			});

			// Handle final response
			responseDisposable = this.webSocketService.onMessage((message: any) => {
				if (message.type === MessageType.CODEBASE_EMBEDDING_BATCH_RESPONSE &&
					message.payload?.requestId === requestId) {

					progressDisposable?.dispose();
					responseDisposable?.dispose();

					const { embeddings, errors, successfullyStored } = message.payload;
					let successCount = 0;

					// Process embeddings
					for (const embedding of embeddings) {
						const chunk = chunks.find(c => c.id === embedding.chunkId);
						if (chunk && embedding.embedding.length > 0) {
							// Cache embedding
							this.embeddingCache.set(embedding.chunkId, embedding.embedding);
							// Add to search engine
							this.searchEngine.addVector(embedding.chunkId, embedding.embedding, {
								filePath: chunk.filePath,
								type: chunk.type,
								name: chunk.name,
								language: chunk.language
							});
							successCount++;
						}
					}

					// Log errors
					if (errors && errors.length > 0) {
						for (const error of errors) {
							this.logService.error(`[CodebaseIndexingService] Embedding error for chunk ${error.chunkId}: ${error.error}`);
						}
					}

					// Mark files as indexed based on successful embeddings
					const successfulFiles = new Set<string>();
					for (const embedding of embeddings) {
						const chunk = chunks.find(c => c.id === embedding.chunkId);
						if (chunk && embedding.embedding.length > 0) {
							successfulFiles.add(chunk.filePath);
						}
					}

					// Update file status in recent files map based on success/failure
					filePaths.forEach(filePath => {
						const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || filePath;
						let status: IRecentFileActivity['status'] = 'indexed';
						let errorDetails: string | undefined;

						if (!successfulFiles.has(filePath)) {
							// Check if this file had errors
							const fileHadErrors = errors?.some((error: any) => {
								const chunk = chunks.find(c => c.id === error.chunkId);
								return chunk && chunk.filePath === filePath;
							});
							if (fileHadErrors) {
								status = 'error';
								const fileErrors = errors?.filter((error: any) => {
									const chunk = chunks.find(c => c.id === error.chunkId);
									return chunk && chunk.filePath === filePath;
								});
								errorDetails = fileErrors?.map((e: any) => e.error).join(', ');
							}
						}

						this._recentlyProcessedFilesMap.set(filePath, {
							filePath,
							fileName,
							status,
							timestamp: new Date(),
							errorDetails
						});
					});

					this.logService.info(`[CodebaseIndexingService] Successfully embedded ${successCount}/${chunks.length} chunks, ${successfullyStored || successCount} stored in Pinecone`);

					resolve();
				}
			});

			// Increased timeout to 10 minutes for very slow rate limits
			setTimeout(() => {
				progressDisposable?.dispose();
				responseDisposable?.dispose();
				this.logService.error('[CodebaseIndexingService] Embedding request timed out after 10 minutes');
				reject(new Error('Embedding request timed out'));
			}, 600000); // 10 minutes for 5 RPM rate limit
		});
	}
}

// Register the service
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
registerSingleton(ICodebaseIndexingService, CodebaseIndexingService, InstantiationType.Delayed);