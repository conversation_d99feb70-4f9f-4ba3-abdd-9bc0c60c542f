/*---------------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------------*/

import { IUserData } from '../common/voidAuthTypes.js';

/**
 * Message types for WebSocket communication - matching monorepo implementation
 */
export enum MessageType {
	// Connection messages
	CONNECT_SUCCESS = 'connect_success',
	CONNECT_ERROR = 'connect_error',

	// Authentication messages
	AUTHENTICATE = 'authenticate',
	AUTH_SUCCESS = 'auth_success',
	AUTH_FAILURE = 'auth_failure',

	// Keep-alive messages
	PING = 'ping',
	PONG = 'pong',

	// Provider discovery messages
	PROVIDER_LIST = 'provider_list',
	PROVIDER_MODELS = 'provider_models',

	// Provider interaction messages
	PROVIDER_REQUEST = 'provider_request',
	PROVIDER_RESPONSE = 'provider_response',
	PROVIDER_ERROR = 'provider_error',

	// Streaming messages
	PROVIDER_STREAM_START = 'provider_stream_start',
	PROVIDER_STREAM_CHUNK = 'provider_stream_chunk',
	PROVIDER_STREAM_END = 'provider_stream_end',

	// Tool messages
	TOOL_EXECUTION_RESULT = 'tool_execution_result',

	// User data messages
	USER_DATA_REQUEST = 'user_data_request',
	USER_DATA_RESPONSE = 'user_data_response',

	// Codebase indexing messages
	CODEBASE_INDEX_REQUEST = 'codebase_index_request',
	CODEBASE_INDEX_RESPONSE = 'codebase_index_response',
	CODEBASE_EMBEDDING_REQUEST = 'codebase_embedding_request',
	CODEBASE_EMBEDDING_RESPONSE = 'codebase_embedding_response',
	CODEBASE_EMBEDDING_BATCH_REQUEST = 'codebase_embedding_batch_request',
	CODEBASE_EMBEDDING_BATCH_RESPONSE = 'codebase_embedding_batch_response',
	CODEBASE_EMBEDDING_PROGRESS = 'codebase_embedding_progress',
	CODEBASE_SEARCH_REQUEST = 'codebase_search_request',
	CODEBASE_SEARCH_RESPONSE = 'codebase_search_response',
	CODEBASE_CLEAR_INDEX_REQUEST = 'codebase_clear_index_request',
	CODEBASE_CLEAR_INDEX_RESPONSE = 'codebase_clear_index_response',

	// General error
	ERROR = 'error'
}

// Defines the structure for a tool call
export interface ToolCall {
	id: string;
	name: string;
	parameters: Record<string, any>;
}

// Gemini message format
export interface GeminiMessage {
	role: 'user' | 'assistant' | 'system' | 'model' | 'tool';
	parts: { text?: string; inlineData?: { mimeType: string; data: string; } }[];
	content?: string | Record<string, unknown>;
	displayContent?: string;
	reasoning?: string;
	toolCallId?: string;
}

/**
 * Base message interface
 */
interface BaseMessage {
	type: MessageType;
	timestamp?: number;
	payload: Record<string, any>;
}

// --- Client to Server Messages ---

export interface ClientMessage extends BaseMessage {
	type: MessageType;
	payload: any;
}

export interface AuthenticateMessage extends ClientMessage {
	type: MessageType.AUTHENTICATE;
	payload: {
		token: string;
	};
}

export interface ProviderRequestMessage extends ClientMessage {
	type: MessageType.PROVIDER_REQUEST;
	payload: {
		provider: string;
		model: string;
		prompt: string;
		temperature?: number;
		maxTokens?: number;
		stream?: boolean;
		systemMessage?: string;
		tools?: Array<{
			name: string;
			description: string;
			parameters: Record<string, { description: string }>;
		}> | null;
		requestId?: string;
	};
}

export interface ClientProviderListMessage extends ClientMessage {
	type: MessageType.PROVIDER_LIST;
	payload: {};
}

export interface ClientProviderModelsMessage extends ClientMessage {
	type: MessageType.PROVIDER_MODELS;
	payload: {
		provider: string;
	};
}

export interface ClientPingMessage extends ClientMessage {
	type: MessageType.PING;
	payload: {};
}

export interface ClientToolResultMessage extends ClientMessage {
	type: MessageType.TOOL_EXECUTION_RESULT;
	payload: {
		requestId: string;
		toolCallId: string;
		toolName: string;
		result: any;
		isError: boolean;
		errorDetails?: string;
	};
}

export interface UserDataRequestMessage extends ClientMessage {
	type: MessageType.USER_DATA_REQUEST;
	payload: {
		userId: string;
	};
}

// Codebase indexing messages
export interface CodebaseIndexRequestMessage extends ClientMessage {
	type: MessageType.CODEBASE_INDEX_REQUEST;
	payload: {
		paths?: string[];
		options?: any;
	};
}

export interface CodebaseEmbeddingRequestMessage extends ClientMessage {
	type: MessageType.CODEBASE_EMBEDDING_REQUEST;
	payload: {
		chunk: any;
		requestId: string;
	};
}

export interface CodebaseEmbeddingBatchRequestMessage extends ClientMessage {
	type: MessageType.CODEBASE_EMBEDDING_BATCH_REQUEST;
	payload: {
		chunks: any[];
		requestId: string;
		batchId: string;
	};
}

export interface CodebaseSearchRequestMessage extends ClientMessage {
	type: MessageType.CODEBASE_SEARCH_REQUEST;
	payload: {
		query: string;
		options?: any;
		requestId: string;
	};
}

export interface CodebaseClearIndexRequestMessage extends ClientMessage {
	type: MessageType.CODEBASE_CLEAR_INDEX_REQUEST;
	payload: {
		requestId: string;
	};
}

export type ClientToServerMessage =
	| AuthenticateMessage
	| ProviderRequestMessage
	| ClientProviderListMessage
	| ClientProviderModelsMessage
	| ClientPingMessage
	| ClientToolResultMessage
	| UserDataRequestMessage
	| CodebaseIndexRequestMessage
	| CodebaseEmbeddingRequestMessage
	| CodebaseEmbeddingBatchRequestMessage
	| CodebaseSearchRequestMessage
	| CodebaseClearIndexRequestMessage;

// --- Server to Client Messages ---

export interface ServerMessage extends BaseMessage {
	type: MessageType;
	payload: any;
}

export interface ConnectSuccessMessage extends ServerMessage {
	type: MessageType.CONNECT_SUCCESS;
	payload: {
		connectionId: string;
		userId: string | null;
		serverTime: string;
		serverInfo: {
			environment: string;
		};
	};
}

export interface AuthSuccessMessage extends ServerMessage {
	type: MessageType.AUTH_SUCCESS;
	payload: {
		userId: string;
		connectionId: string;
		user?: IUserData;
		token?: string;
	};
}

export interface AuthFailureMessage extends ServerMessage {
	type: MessageType.AUTH_FAILURE;
	payload: {
		error: string;
		message: string;
	};
}

export interface ProviderListMessage extends ServerMessage {
	type: MessageType.PROVIDER_LIST;
	payload: {
		providers: Array<{
			id: string;
			name: string;
			available: boolean;
		}>;
		defaultProvider: string;
	};
}

export interface ProviderModelsMessage extends ServerMessage {
	type: MessageType.PROVIDER_MODELS;
	payload: {
		provider: string;
		available: boolean;
		models: Array<{
			id: string;
			name: string;
			provider: string;
			available: boolean;
			contextWindow: number;
			maxOutputTokens: number;
			features: string[];
		}>;
	};
}

export interface ProviderStreamStartMessage extends ServerMessage {
	type: MessageType.PROVIDER_STREAM_START;
	payload: {
		provider: string;
		model: string;
		requestId: string;
	};
}

export interface ProviderStreamChunkMessage extends ServerMessage {
	type: MessageType.PROVIDER_STREAM_CHUNK;
	payload: {
		chunk: string;
		requestId: string;
		toolCallUpdate?: {
			name: string;
			parameters: Record<string, unknown>;
			id?: string;
		};
	};
}

export interface ProviderStreamEndMessage extends ServerMessage {
	type: MessageType.PROVIDER_STREAM_END;
	payload: {
		requestId: string;
		tokensUsed: number;
		success: boolean;
		error?: string;
		toolCall?: {
			name: string;
			parameters: Record<string, unknown>;
			id: string;
		};
		waitingForToolCall?: boolean;
	};
}

export interface ErrorMessage extends ServerMessage {
	type: MessageType.ERROR;
	payload: {
		error: string;
		code: string;
		message?: string;
	};
}

export interface ServerPongMessage extends ServerMessage {
	type: MessageType.PONG;
	payload: {};
}

export interface UserDataResponseMessage extends ServerMessage {
	type: MessageType.USER_DATA_RESPONSE;
	payload: {
		user?: IUserData;
		error?: string;
	};
}

export interface ProviderErrorMessage extends ServerMessage {
	type: MessageType.PROVIDER_ERROR;
	payload: {
		error: string;
		code: string;
		requestId?: string;
	};
}

// Codebase indexing response messages
export interface CodebaseIndexResponseMessage extends ServerMessage {
	type: MessageType.CODEBASE_INDEX_RESPONSE;
	payload: {
		success: boolean;
		error?: string;
		stats?: any;
	};
}

export interface CodebaseEmbeddingResponseMessage extends ServerMessage {
	type: MessageType.CODEBASE_EMBEDDING_RESPONSE;
	payload: {
		requestId: string;
		embedding?: number[];
		error?: string;
	};
}

export interface CodebaseEmbeddingBatchResponseMessage extends ServerMessage {
	type: MessageType.CODEBASE_EMBEDDING_BATCH_RESPONSE;
	payload: {
		requestId: string;
		batchId: string;
		embeddings: Array<{
			chunkId: string;
			embedding: number[];
		}>;
		errors?: Array<{
			chunkId: string;
			error: string;
		}>;
	};
}

export interface CodebaseEmbeddingProgressMessage extends ServerMessage {
	type: MessageType.CODEBASE_EMBEDDING_PROGRESS;
	payload: {
		requestId: string;
		batchId: string;
		completed: number;
		total: number;
		currentBatch: number;
		totalBatches: number;
		percentage: number;
	};
}

export interface CodebaseSearchResponseMessage extends ServerMessage {
	type: MessageType.CODEBASE_SEARCH_RESPONSE;
	payload: {
		requestId: string;
		results: any[];
		error?: string;
		stats?: {
			vectorCount: number;
			namespace: string;
		};
	};
}

export interface CodebaseClearIndexResponseMessage extends ServerMessage {
	type: MessageType.CODEBASE_CLEAR_INDEX_RESPONSE;
	payload: {
		requestId: string;
		success: boolean;
		error?: string;
		deletedVectorCount?: number;
	};
}

export type ServerToClientMessage =
	| ConnectSuccessMessage
	| AuthSuccessMessage
	| AuthFailureMessage
	| ProviderListMessage
	| ProviderModelsMessage
	| ProviderStreamStartMessage
	| ProviderStreamChunkMessage
	| ProviderStreamEndMessage
	| ErrorMessage
	| ServerPongMessage
	| UserDataResponseMessage
	| ProviderErrorMessage
	| CodebaseIndexResponseMessage
	| CodebaseEmbeddingResponseMessage
	| CodebaseEmbeddingBatchResponseMessage
	| CodebaseEmbeddingProgressMessage
	| CodebaseSearchResponseMessage
	| CodebaseClearIndexResponseMessage;

// WebSocket connection status
export type WebSocketStatus = 'disconnected' | 'connecting' | 'authenticating' | 'connected' | 'error' | 'reconnecting';
