# WebSocket Server Configuration

# Server Settings
WS_PORT=8080
WS_HOST=0.0.0.0
WS_PATH=/ws
NODE_ENV=development
PING_INTERVAL=30000

# Authentication
AUTH_ENABLED=true
CLERK_SECRET_KEY=your_clerk_secret_key_here
CLERK_JWT_KEY=your_clerk_jwt_key_here

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Default AI Provider
DEFAULT_PROVIDER=gemini

# AI Provider API Keys
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX_NAME=coode-codebase
PINECONE_NAMESPACE=default

# Embedding Configuration
# Use stable text-embedding-004 model for better reliability
EMBEDDING_MODEL=text-embedding-004
EMBEDDING_API_VERSION=v1beta
# Increased batch size for better throughput
EMBEDDING_BATCH_SIZE=10
# Increased rate limit to 60 RPM (1 per second) for better performance
EMBEDDING_RATE_LIMIT=60

# Logging
LOG_LEVEL=info
